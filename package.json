{"name": "discord", "version": "1.0.0", "description": "", "main": "index.js", "engines": {"node": "18.12.1"}, "dependencies": {"@discordjs/builders": "^1.3.0", "@discordjs/voice": "^0.13.0", "discord.js": "^14.6.0", "express": "^4.21.2", "ffmpeg-static": "^5.1.0", "got": "^11.7.0", "jsdom": "^16.4.0", "libsodium-wrappers": "^0.7.10", "opusscript": "^0.0.7"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/DanielJulian/discordBot.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Daniel<PERSON>/discordBot/issues"}, "homepage": "https://github.com/<PERSON><PERSON><PERSON>/discordBot#readme"}