const fs = require('fs');
const path = require('path');

// Function to recursively get all .js files in commands directory
function getCommandFiles(dir = './commands') {
    let files = [];
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            files = files.concat(getCommandFiles(fullPath));
        } else if (item.endsWith('.js')) {
            files.push(fullPath);
        }
    }
    
    return files;
}

// Function to fix a command file
function fixCommandFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Check if file contains playAudio call
    if (content.includes('playAudio(')) {
        // Replace playAudio( with await playAudio( if not already awaited
        const regex = /(?<!await\s+)playAudio\(/g;
        if (regex.test(content)) {
            content = content.replace(/(?<!await\s+)playAudio\(/g, 'await playAudio(');
            modified = true;
        }
    }
    
    if (modified) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed: ${filePath}`);
        return true;
    }
    
    return false;
}

// Main execution
console.log('Starting to fix command files...');
const commandFiles = getCommandFiles();
let fixedCount = 0;

for (const file of commandFiles) {
    if (fixCommandFile(file)) {
        fixedCount++;
    }
}

console.log(`Fixed ${fixedCount} command files.`);
