const { getRandomFileFromFolderRecursive } = require("../Utils");
const { SlashCommandBuilder } = require('@discordjs/builders');
const { AttachmentBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('cringe')
    .setDescription('Una fea sorpresa...'),
  async execute(interaction) {
    await interaction.reply({ files: [new AttachmentBuilder(getRandomFileFromFolderRecursive("./images/cringe/"))] });
  },
};