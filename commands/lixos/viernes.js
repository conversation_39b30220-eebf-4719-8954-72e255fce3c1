const { playAudio, getRandomFileFromFolder } = require("../../Utils");;
const { SlashCommandBuilder } = require('@discordjs/builders');
const { MessageAttachment } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('viernes')
        .setDescription('Viernesss'),
    async execute(interaction) {
        if (new Date().getDay() === 5) {
            try {
                await interaction.reply({ attachment: [new MessageAttachment("./images/ficha_viernes.jpg")] });
            } catch (e) {}
            playAudio(getRandomFileFromFolder("./audios/Otros/Viernes/"), interaction);
        }
        else if (new Date().getDay() === 6) {
            playAudio("./audios/Otros/No es Viernes/tarde.ogg", interaction);
        }
        else if (new Date().getDay() === 4) {
            playAudio("./audios/Otros/No es Viernes/Gutin - Hoy no es Viernes.ogg", interaction);
        }
        else if (new Date().getDay() === 1) {
            playAudio("./audios/Otros/No es Viernes/fede_lunes.ogg", interaction);
        }
        else {
            await interaction.reply("Hoy no es viernes :(")
        }
    },
};