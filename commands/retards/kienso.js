const { MessageAttachment } = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('kienso')
        .setDescription('Quien chota sos amigo?'),
    async execute(interaction) {
        await interaction.reply({ attachment: [new MessageAttachment("./images/quien_chota_sos_amigo.png")] });
    },
};