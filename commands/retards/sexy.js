const { getRandomFileFromFolder } = require("../../Utils");;
const { SlashCommandBuilder } = require('@discordjs/builders');
const { MessageAttachment } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('sexy')
        .setDescription('sexy images mai frend'),
    async execute(interaction) {
        await interaction.reply({ attachment: [new MessageAttachment(getRandomFileFromFolder("./images/sexy/"))] });
    },
};