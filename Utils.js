const { joinVoiceChannel, createAudioPlayer, createAudioResource, AudioPlayerStatus } = require("@discordjs/voice")
const path = require('path');
const fs = require('fs');

let disconnectTimeout; // Used so the bot disconnects after beign idle for x amount of time
let currentPlayer = null; // Store the current audio player to reuse it

module.exports.playAudio = async function playAudio(audioPath, interaction) {
    disconnectTimeoutClear();

    // Respond to interaction immediately to avoid timeout
    if (!interaction.replied && !interaction.deferred) {
        await interaction.deferReply();
    }

    const voiceChannelID = interaction.member.voice.channel?.id;
    const guildID = interaction.guild.id;
    const adapterCreator = interaction.guild.voiceAdapterCreator;

    if (!voiceChannelID) {
        if (interaction.deferred) {
            await interaction.editReply('Metete en un channel de voz macaco...');
        } else if (!interaction.replied) {
            await interaction.reply('Metete en un channel de voz macaco...');
        } else {
            await interaction.followUp('Metete en un channel de voz macaco...');
        }
        return;
    }

    // Reuse the existing player or create a new one
    if (!currentPlayer) {
        currentPlayer = createAudioPlayer();

        currentPlayer.on(AudioPlayerStatus.Playing, () => {
            console.log('The audio player has started playing!');
        });

        currentPlayer.on(AudioPlayerStatus.Idle, () => {
            console.log('The audio player is now idle (finished playing)');
        });

        currentPlayer.on(AudioPlayerStatus.Paused, () => {
            console.log('The audio player has been paused');
        });

        currentPlayer.on('error', error => {
            console.error(`Error: ${error.message} with resource`);
        });
    }

    const player = currentPlayer;

    const connection = joinVoiceChannel({
        channelId: voiceChannelID,
        guildId: guildID,
        adapterCreator: adapterCreator,
    })

    // Add connection error handling
    connection.on('error', error => {
        console.error('Voice connection error:', error);
    });

    connection.on('stateChange', (oldState, newState) => {
        console.log(`Voice connection state changed from ${oldState.status} to ${newState.status}`);
    });

    connection.subscribe(player)
    const resource = createAudioResource(audioPath, {
        inlineVolume: true
    })
    player.play(resource)

    disconnectTimeoutStart(connection)

    // Send the success message
    if (interaction.deferred) {
        await interaction.editReply("Ahi va el audio masivo");
    } else if (!interaction.replied) {
        await interaction.reply("Ahi va el audio masivo");
    } else {
        await interaction.followUp("Ahi va el audio masivo");
    }
}


module.exports.getRandomFileFromFolder = function getRandomFileFromFolder(folder) {
    let files = fs.readdirSync(folder);
    return folder + files[Math.floor(Math.random() * files.length)];
};

module.exports.getRandomFileFromFolderRecursive = function getRandomFileFromFolderRecursive(folder) {
    let files = [];
    getFilesRecursively(folder, files);
    return files[Math.floor(Math.random() * files.length)];
};

module.exports.getCommandFiles = function getCommandFiles() {
    let baseFolder = './commands';
    let files = [];
    getFilesRecursively(baseFolder, files);
    return files;
};

function getFilesRecursively(base, list, currentFiles) {
    currentFiles = currentFiles || fs.readdirSync(base);
    currentFiles.forEach(
        function (file) {
            let newbase = path.join(base, file);
            if (fs.statSync(newbase).isDirectory()) {
                getFilesRecursively(newbase, list, fs.readdirSync(newbase));
            } else {
                list.push('./' + newbase);
            }
        }
    )
}


function disconnectTimeoutStart(connection) {
    disconnectTimeout = setTimeout(() => {
        connection.destroy();
        // Clean up the player when connection is destroyed
        if (currentPlayer) {
            currentPlayer.stop();
            currentPlayer = null;
        }
    }, 300000) // 5 minutos de timeout
}

function disconnectTimeoutClear() {
    if (disconnectTimeout != null) {
        clearTimeout(disconnectTimeout);
        disconnectTimeout = null;
    }
}

